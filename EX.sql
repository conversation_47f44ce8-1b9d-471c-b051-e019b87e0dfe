create table regions(REGION_ID: number, REGION_NAME: varchar(20), CONSTRAINT REGION_ID PRIMARY KEY);

create table counteries(COUNTRIES_ID: char(20), COUNTRY_NAME: varchar(20), #REGION_ID: number, CONSTRAINT REGION_ID PRIMARY KEY, CONSTRAINT REGION_ID FOREIGN KEY REFRENCES  );
create table locations(LOCATION_ID: number, street_address: varchar(25), postal_code: varchar(12), city varchar(30), state_province varchar(12), #Country_ID char(2), CONSTRAINT location_ID PRIMARY KEY, CONSTRAINT COUNTRY_ID FOREIGN KEY REFRENCES  );
create table departments(DEPARTMENT_ID: number, DEPARTMENT_NAME: varchar(30), #MANAGER_ID: number, #LOCATION_ID: number, CONSTRAINT DEPARTMENT_ID PRIMARY KEY, CONSTRAINT MANAGER_ID FOREIGN KEY REFRENCES  , CONS<PERSON>AINT LOCATION_ID FOREIGN KEY REFRENCES  );
create table job_history(EMPLOYEE_ID: number, START_DATE: date, END_DATE: date, #JOB_ID: varchar(10), #DEPARTMENT_ID: number, CONSTRAINT EMPLOYEE_ID PRIMARY KEY, CONSTRAINT JOB_ID FOREIGN KEY REFRENCES  , CONSTRAINT DEPARTMENT_ID FOREIGN KEY REFRENCES  );
create table jobs(JOB_ID: varchar(10), JOB_TITLE: varchar(35), MIN_SALARY: number, MAX_SALARY: number, CONSTRAINT JOB_ID PRIMARY KEY);

create table job_grades(GRADE_LEVEL: char(1), LOWEST_SAL: number, HIGHEST_SAL: number, CONSTRAINT GRADE_LEVEL PRIMARY KEY);

create table employees(
    EMPLOYEE_ID: number, 
    FIRST_NAME: varchar(20), 
    LAST_NAME: varchar(25), 
    EMAIL: varchar(25), 
    PHONE_NUMBER: varchar(20), 
    HIRE_DATE: date, 
    #JOB_ID: varchar(10), 
    SALARY: number, 
    COMMISSION_PCT: number, 
    #MANAGER_ID: number, 
    #DEPARTMENT_ID: number, 
    CONSTRAINT EMPLOYEE_ID PRIMARY KEY, 
    CONSTRAINT JOB_ID FOREIGN KEY REFERENCES jobs(JOB_ID), 
    CONSTRAINT MANAGER_ID FOREIGN KEY REFERENCES employees(EMPLOYEE_ID), 
    CONSTRAINT DEPARTMENT_ID FOREIGN KEY REFERENCES departments(DEPARTMENT_ID)
);